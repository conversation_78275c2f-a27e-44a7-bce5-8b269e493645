#!/usr/bin/env python3
"""
Complete System Test
===================

Test the complete enhanced NIFTY 50 EMA crossover trading system with:
- Professional project structure
- Historical database with .pkl and .csv formats
- OHLC data in signal logging
- Clean directory organization
- Backup management

Author: AI Assistant
Date: 2025
"""

import sys
import os
import json
from datetime import datetime

# Add src directory to path
sys.path.append('src')

from core.ema import EMACalculator
from core.strategy import EMAStrategy
from data.logger import SignalLogger
from data.historical_database import HistoricalDatabase
from utils.market_hours import MarketHoursManager
from utils.state_manager import StateManager


def test_project_structure():
    """Test the professional project structure"""
    print("🧪 Testing Project Structure...")
    
    required_dirs = [
        'src/core',
        'src/data', 
        'src/utils',
        'data/historical',
        'data/signals',
        'data/state',
        'config',
        'tests',
        'scripts',
        'notebooks',
        'docs',
        'backup'
    ]
    
    required_files = [
        'README.md',
        'setup.py',
        'requirements.txt',
        'requirements-dev.txt',
        '.gitignore',
        'src/__init__.py',
        'src/core/__init__.py',
        'src/data/__init__.py',
        'src/utils/__init__.py',
        'config/config.json.example'
    ]
    
    missing_dirs = [d for d in required_dirs if not os.path.exists(d)]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_dirs:
        print(f"  ❌ Missing directories: {missing_dirs}")
        return False
    
    if missing_files:
        print(f"  ❌ Missing files: {missing_files}")
        return False
    
    print("  ✅ All required directories and files present")
    return True


def test_historical_database():
    """Test historical database functionality"""
    print("\n🧪 Testing Historical Database...")
    
    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)
    
    # Create historical database
    db = HistoricalDatabase(
        dhan_credentials=config['dhan_credentials'],
        market_hours_config=config.get('market_hours', {}),
        data_directory="data"
    )
    
    # Test database info
    db_info = db.get_database_info()
    print(f"  Database status: {db_info['status']}")
    print(f"  Total days: {db_info['total_days']}")
    print(f"  Total candles: {db_info['total_candles']}")
    
    # Check both .pkl and .csv files exist
    pkl_exists = os.path.exists('data/historical/nifty50_historical.pkl')
    csv_exists = os.path.exists('data/historical/nifty50_historical.csv')
    
    print(f"  PKL file exists: {pkl_exists}")
    print(f"  CSV file exists: {csv_exists}")
    
    if pkl_exists and csv_exists:
        pkl_size = os.path.getsize('data/historical/nifty50_historical.pkl') / (1024 * 1024)
        csv_size = os.path.getsize('data/historical/nifty50_historical.csv') / (1024 * 1024)
        print(f"  PKL size: {pkl_size:.2f} MB")
        print(f"  CSV size: {csv_size:.2f} MB")
    
    return pkl_exists and csv_exists and db_info['total_days'] > 0


def test_enhanced_signal_logging():
    """Test enhanced signal logging with OHLC data"""
    print("\n🧪 Testing Enhanced Signal Logging...")
    
    # Create signal logger
    signal_logger = SignalLogger("test_data", 100000)
    
    # Test signal with OHLC data
    test_signal = {
        'datetime': datetime.now(),
        'timeframe': '1min',
        'action': 'BUY',
        'price': 24750.50,
        'ohlc': {
            'open': 24745.00,
            'high': 24755.00,
            'low': 24740.00,
            'close': 24750.50,
            'volume': 1500
        },
        'short_ema_value': 24748.25,
        'long_ema_value': 24745.80,
        'pnl': 0.0
    }
    
    # Log the signal
    signal_logger.log_signal(test_signal)
    
    # Check if CSV has OHLC columns
    csv_file = f"test_data/nifty50_ema_signals_{datetime.now().strftime('%Y%m%d')}.csv"
    
    if os.path.exists(csv_file):
        with open(csv_file, 'r') as f:
            header = f.readline().strip()
        
        required_columns = ['Date', 'Time', 'Action', 'Price', 'Open', 'High', 'Low', 'Close', 'Volume', 'EMA5_Value', 'EMA10_Value']
        has_ohlc = all(col in header for col in required_columns)
        
        print(f"  CSV header: {header}")
        print(f"  Has OHLC columns: {has_ohlc}")
        
        signal_logger.close()
        return has_ohlc
    
    signal_logger.close()
    return False


def test_component_integration():
    """Test integration between components"""
    print("\n🧪 Testing Component Integration...")
    
    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)
    
    # Create components
    market_hours_manager = MarketHoursManager(
        config=config.get('market_hours', {}),
        data_directory="test_data"
    )
    
    ema_calculator = EMACalculator(config['ema_combinations'])
    signal_logger = SignalLogger("test_data", 100000)
    state_manager = StateManager("test_data")
    
    strategy = EMAStrategy(
        ema_combinations=config['ema_combinations'],
        timeframes=config['timeframes'],
        ema_calculator=ema_calculator,
        signal_logger=signal_logger,
        state_manager=state_manager
    )
    
    # Test market hours
    market_status = market_hours_manager.get_market_status_string()
    print(f"  Market status: {market_status}")
    
    # Test EMA calculation
    test_price = 24750.0
    emas = ema_calculator.add_price("1min", test_price)
    print(f"  EMA calculation: {emas is not None}")
    
    # Test strategy processing
    test_tick = {
        'timestamp': datetime.now(),
        'price': test_price,
        'volume': 1000
    }
    
    try:
        strategy.process_tick(test_tick)
        print("  ✅ Strategy processing successful")
        integration_success = True
    except Exception as e:
        print(f"  ❌ Strategy processing failed: {e}")
        integration_success = False
    
    signal_logger.close()
    return integration_success


def test_backup_organization():
    """Test backup directory organization"""
    print("\n🧪 Testing Backup Organization...")
    
    backup_dirs = [
        'backup/old_files',
        'backup/old_docs', 
        'backup/old_tests',
        'backup/old_data'
    ]
    
    all_exist = all(os.path.exists(d) for d in backup_dirs)
    
    if all_exist:
        # Count files in backup directories
        total_backup_files = 0
        for backup_dir in backup_dirs:
            if os.path.exists(backup_dir):
                files = os.listdir(backup_dir)
                total_backup_files += len(files)
                print(f"  {backup_dir}: {len(files)} files")
        
        print(f"  ✅ Total backup files: {total_backup_files}")
        return True
    else:
        missing = [d for d in backup_dirs if not os.path.exists(d)]
        print(f"  ❌ Missing backup directories: {missing}")
        return False


def test_documentation():
    """Test documentation completeness"""
    print("\n🧪 Testing Documentation...")
    
    doc_files = [
        'README.md',
        'docs/ARCHITECTURE.md',
        'docs/DEPLOYMENT.md'
    ]
    
    missing_docs = [f for f in doc_files if not os.path.exists(f)]
    
    if missing_docs:
        print(f"  ❌ Missing documentation: {missing_docs}")
        return False
    
    # Check README content
    with open('README.md', 'r') as f:
        readme_content = f.read()
    
    required_sections = [
        'Features',
        'Architecture', 
        'Installation',
        'Configuration',
        'Usage',
        'Data Management'
    ]
    
    missing_sections = [s for s in required_sections if s not in readme_content]
    
    if missing_sections:
        print(f"  ❌ Missing README sections: {missing_sections}")
        return False
    
    print("  ✅ All documentation present and complete")
    return True


def main():
    """Run complete system tests"""
    print("=" * 60)
    print("COMPLETE ENHANCED SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("Project Structure", test_project_structure),
        ("Historical Database", test_historical_database),
        ("Enhanced Signal Logging", test_enhanced_signal_logging),
        ("Component Integration", test_component_integration),
        ("Backup Organization", test_backup_organization),
        ("Documentation", test_documentation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"FINAL RESULTS: {passed}/{total} TESTS PASSED")
    print("=" * 60)
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! SYSTEM IS PRODUCTION READY!")
        print("\n✨ Enhanced Features Successfully Implemented:")
        print("  ✅ Professional project structure")
        print("  ✅ 2-week rolling historical database (.pkl + .csv)")
        print("  ✅ OHLC data in signal logging")
        print("  ✅ Clean directory organization")
        print("  ✅ Comprehensive backup management")
        print("  ✅ Complete documentation")
        print("  ✅ Modular component architecture")
        
        print("\n🚀 Ready for Production Deployment!")
        print("  python ema_daemon.py start")
        
    else:
        print(f"⚠️  {total - passed} tests failed. Review the output above.")
    
    print("\n📊 System Overview:")
    print("  • Historical Database: 14-day rolling window with dual storage")
    print("  • Signal Logging: Enhanced with OHLC data")
    print("  • Project Structure: Professional Python package layout")
    print("  • Documentation: Complete with architecture and deployment guides")
    print("  • Backup Management: Organized legacy file storage")


if __name__ == "__main__":
    main()
