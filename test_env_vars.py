#!/usr/bin/env python3
"""
Test Environment Variables Loading
==================================

Simple test to verify that environment variables are loaded correctly
without requiring python-dotenv initially.
"""

import os
import sys

def load_env_file(env_file='.env'):
    """Simple .env file loader without python-dotenv dependency"""
    env_vars = {}
    try:
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value
        return env_vars
    except FileNotFoundError:
        print(f"❌ {env_file} file not found")
        return {}
    except Exception as e:
        print(f"❌ Error loading {env_file}: {e}")
        return {}

def test_credentials():
    """Test credential loading"""
    print("🧪 Testing Environment Variable Loading")
    print("=" * 50)
    
    # Load .env file
    env_vars = load_env_file('.env')
    
    if env_vars:
        print(f"✅ Loaded {len(env_vars)} environment variables from .env")
        for key in env_vars:
            if 'TOKEN' in key:
                # Mask sensitive values
                value = env_vars[key]
                masked = value[:10] + "..." + value[-10:] if len(value) > 20 else "***"
                print(f"  {key}: {masked}")
            else:
                print(f"  {key}: {env_vars[key]}")
    else:
        print("❌ No environment variables loaded")
        return False
    
    # Test credential access
    client_id = os.getenv('DHAN_CLIENT_ID')
    access_token = os.getenv('DHAN_ACCESS_TOKEN')
    
    print("\n🔑 Testing Credential Access")
    print("-" * 30)
    
    if client_id:
        print(f"✅ DHAN_CLIENT_ID: {client_id}")
    else:
        print("❌ DHAN_CLIENT_ID not found")
        return False
    
    if access_token:
        masked_token = access_token[:10] + "..." + access_token[-10:]
        print(f"✅ DHAN_ACCESS_TOKEN: {masked_token}")
    else:
        print("❌ DHAN_ACCESS_TOKEN not found")
        return False
    
    return True

def test_config_loading():
    """Test config loading without credentials"""
    print("\n📋 Testing Config Loading")
    print("-" * 30)
    
    try:
        import json
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        
        print("✅ Config file loaded successfully")
        print(f"  Instrument: {config.get('instrument', {}).get('name', 'Unknown')}")
        print(f"  EMA combinations: {len(config.get('ema_combinations', []))}")
        print(f"  Timeframes: {config.get('timeframes', [])}")
        
        # Check that credentials are NOT in config
        if 'dhan_credentials' in config:
            print("❌ WARNING: dhan_credentials still found in config file!")
            return False
        else:
            print("✅ No credentials found in config file (good!)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Environment Variables Migration Test")
    print("=" * 60)
    
    success = True
    
    # Test 1: Environment variable loading
    if not test_credentials():
        success = False
    
    # Test 2: Config loading
    if not test_config_loading():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Environment variables are working correctly")
        print("✅ Credentials are properly secured")
        print("✅ Config file is clean of sensitive data")
        print("\n🚀 Ready to test the main application!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the issues above before proceeding.")
    
    print("=" * 60)
