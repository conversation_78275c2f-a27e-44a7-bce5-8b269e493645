# Deployment Guide

## Production Deployment

### System Requirements

#### Hardware
- **CPU**: 2+ cores, 2.0+ GHz
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB available space
- **Network**: Stable internet connection (1Mbps+)

#### Software
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, Windows
- **Python**: 3.8 or higher
- **Dependencies**: See requirements.txt

### Pre-Deployment Checklist

#### 1. Environment Setup
```bash
# Create dedicated user
sudo useradd -m -s /bin/bash ema-trader
sudo usermod -aG sudo ema-trader

# Switch to trading user
sudo su - ema-trader

# Create application directory
mkdir -p /home/<USER>/trading
cd /home/<USER>/trading
```

#### 2. Application Installation
```bash
# Clone repository
git clone https://github.com/your-username/nifty50-ema-trading.git
cd nifty50-ema-trading

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### 3. Configuration
```bash
# Copy configuration template
cp config/config.json.example config/config.json

# Create environment file with credentials
cp .env.example .env
nano .env  # Add your DhanHQ credentials

# Set proper permissions
chmod 600 .env
chmod 700 data/
```

#### 4. Directory Structure
```bash
# Ensure all directories exist
mkdir -p data/{historical,signals,logs,state}
mkdir -p logs

# Set permissions
chmod 755 data/
chmod 755 logs/
```

### Systemd Service Setup (Linux)

#### 1. Create Service File
```bash
sudo nano /etc/systemd/system/ema-trading.service
```

```ini
[Unit]
Description=NIFTY 50 EMA Crossover Trading System
After=network.target
Wants=network-online.target

[Service]
Type=forking
User=ema-trader
Group=ema-trader
WorkingDirectory=/home/<USER>/trading/nifty50-ema-trading
Environment=PATH=/home/<USER>/trading/nifty50-ema-trading/venv/bin
ExecStart=/home/<USER>/trading/nifty50-ema-trading/venv/bin/python ema_daemon.py start
ExecStop=/home/<USER>/trading/nifty50-ema-trading/venv/bin/python ema_daemon.py stop
ExecReload=/home/<USER>/trading/nifty50-ema-trading/venv/bin/python ema_daemon.py restart
PIDFile=/home/<USER>/trading/nifty50-ema-trading/ema_system.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 2. Enable and Start Service
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable service
sudo systemctl enable ema-trading

# Start service
sudo systemctl start ema-trading

# Check status
sudo systemctl status ema-trading
```

### Docker Deployment

#### 1. Create Dockerfile
```dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create data directories
RUN mkdir -p data/{historical,signals,logs,state}

# Set permissions
RUN chmod +x ema_daemon.py manage_historical_data.py

# Expose ports (if needed for monitoring)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python ema_daemon.py status || exit 1

# Run application
CMD ["python", "ema_daemon.py", "start"]
```

#### 2. Docker Compose
```yaml
version: '3.8'

services:
  ema-trading:
    build: .
    container_name: ema-trading
    restart: unless-stopped
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - PYTHONUNBUFFERED=1
      - EMA_LOG_LEVEL=INFO
    networks:
      - trading-network

  # Optional: Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    networks:
      - trading-network

networks:
  trading-network:
    driver: bridge
```

#### 3. Build and Run
```bash
# Build image
docker build -t ema-trading .

# Run with docker-compose
docker-compose up -d

# Check logs
docker-compose logs -f ema-trading
```

### Cloud Deployment (AWS)

#### 1. EC2 Instance Setup
```bash
# Launch EC2 instance (t3.small recommended)
# Security Group: Allow SSH (22), HTTPS (443)
# Key Pair: Create or use existing

# Connect to instance
ssh -i your-key.pem ubuntu@your-instance-ip

# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install -y python3 python3-pip python3-venv git
```

#### 2. Application Deployment
```bash
# Clone and setup application
git clone https://github.com/your-username/nifty50-ema-trading.git
cd nifty50-ema-trading

# Setup virtual environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Configure application
cp config/config.json.example config/config.json
# Edit with your credentials
```

#### 3. Auto-Scaling Setup
```bash
# Create launch template with user data
#!/bin/bash
cd /home/<USER>/nifty50-ema-trading
source venv/bin/activate
python ema_daemon.py start
```

### Monitoring Setup

#### 1. System Monitoring
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Setup log monitoring
sudo apt install -y logwatch
```

#### 2. Application Monitoring
```bash
# Create monitoring script
cat > monitor.sh << 'EOF'
#!/bin/bash
while true; do
    echo "$(date): Checking system status"
    python ema_daemon.py status

    # Check disk space
    df -h | grep -E "/$|/home"

    # Check memory usage
    free -h

    sleep 300  # Check every 5 minutes
done
EOF

chmod +x monitor.sh
```

#### 3. Alerting Setup
```bash
# Install mail utilities
sudo apt install -y mailutils

# Create alert script
cat > alert.sh << 'EOF'
#!/bin/bash
if ! python ema_daemon.py status > /dev/null 2>&1; then
    echo "EMA Trading System is down!" | mail -s "Trading System Alert" <EMAIL>
fi
EOF

# Add to crontab
echo "*/5 * * * * /home/<USER>/trading/nifty50-ema-trading/alert.sh" | crontab -
```

### Backup and Recovery

#### 1. Data Backup
```bash
# Create backup script
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"

mkdir -p $BACKUP_DIR

# Backup data
tar -czf $BACKUP_DIR/data_$DATE.tar.gz data/

# Backup configuration
tar -czf $BACKUP_DIR/config_$DATE.tar.gz config/

# Backup logs (last 7 days)
find logs/ -name "*.log" -mtime -7 | tar -czf $BACKUP_DIR/logs_$DATE.tar.gz -T -

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x backup.sh

# Schedule daily backups
echo "0 2 * * * /home/<USER>/trading/nifty50-ema-trading/backup.sh" | crontab -
```

#### 2. Recovery Procedures
```bash
# Stop system
python ema_daemon.py stop

# Restore data
tar -xzf backup/data_YYYYMMDD_HHMMSS.tar.gz

# Restore configuration
tar -xzf backup/config_YYYYMMDD_HHMMSS.tar.gz

# Start system
python ema_daemon.py start
```

### Security Hardening

#### 1. System Security
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Configure firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow from trusted-ip-range

# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo systemctl restart ssh
```

#### 2. Application Security
```bash
# Set file permissions
chmod 600 config/config.json
chmod 700 data/
chmod 755 logs/

# Create secrets file
echo "export DHAN_CLIENT_ID='your_client_id'" > .env
echo "export DHAN_ACCESS_TOKEN='your_access_token'" >> .env
chmod 600 .env
```

### Performance Optimization

#### 1. System Optimization
```bash
# Increase file limits
echo "ema-trader soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "ema-trader hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# Optimize network settings
echo "net.core.rmem_max = 16777216" | sudo tee -a /etc/sysctl.conf
echo "net.core.wmem_max = 16777216" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### 2. Application Optimization
```bash
# Use production logging level
export EMA_LOG_LEVEL=WARNING

# Optimize Python
export PYTHONOPTIMIZE=1
export PYTHONDONTWRITEBYTECODE=1
```

### Troubleshooting

#### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x ema_daemon.py
   chmod 600 config/config.json
   ```

2. **Port Already in Use**
   ```bash
   sudo netstat -tulpn | grep :port
   sudo kill -9 PID
   ```

3. **Memory Issues**
   ```bash
   free -h
   sudo systemctl restart ema-trading
   ```

4. **Disk Space**
   ```bash
   df -h
   # Clean old logs
   find logs/ -name "*.log" -mtime +7 -delete
   ```

#### Log Analysis
```bash
# Check system logs
journalctl -u ema-trading -f

# Check application logs
tail -f logs/ema_daemon.log

# Check error patterns
grep -i error logs/ema_daemon.log | tail -20
```
