#!/usr/bin/env python3
"""
Test Environment Variables Migration
===================================

Test script to verify that the migration to environment variables is working correctly.
"""

import os
import sys
import json
from dotenv import load_dotenv

def test_env_loading():
    """Test environment variable loading"""
    print("🧪 Testing Environment Variable Loading")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Test credential access
    client_id = os.getenv('DHAN_CLIENT_ID')
    access_token = os.getenv('DHAN_ACCESS_TOKEN')
    
    print("🔑 Testing Credential Access")
    print("-" * 30)
    
    if client_id:
        print(f"✅ DHAN_CLIENT_ID: {client_id}")
    else:
        print("❌ DHAN_CLIENT_ID not found")
        return False
    
    if access_token:
        masked_token = access_token[:10] + "..." + access_token[-10:]
        print(f"✅ DHAN_ACCESS_TOKEN: {masked_token}")
    else:
        print("❌ DHAN_ACCESS_TOKEN not found")
        return False
    
    return True

def test_config_loading():
    """Test config loading without credentials"""
    print("\n📋 Testing Config Loading")
    print("-" * 30)
    
    try:
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        
        print("✅ Config file loaded successfully")
        print(f"  Instrument: {config.get('instrument', {}).get('name', 'Unknown')}")
        print(f"  EMA combinations: {len(config.get('ema_combinations', []))}")
        print(f"  Timeframes: {config.get('timeframes', [])}")
        
        # Check that credentials are NOT in config
        if 'dhan_credentials' in config:
            print("❌ WARNING: dhan_credentials still found in config file!")
            return False
        else:
            print("✅ No credentials found in config file (good!)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return False

def test_main_system():
    """Test the main system initialization"""
    print("\n🚀 Testing Main System Initialization")
    print("-" * 40)
    
    try:
        # Add src directory to path
        sys.path.append('src')
        
        # Import the main system
        from main import EMATradeSystem
        
        print("✅ Main system imports successful")
        
        # Try to initialize the system
        system = EMATradeSystem('config/config.json')
        print("✅ System initialization successful")
        print(f"  Credentials loaded: {bool(system.config.get('dhan_credentials'))}")
        
        if system.config.get('dhan_credentials'):
            creds = system.config['dhan_credentials']
            print(f"  Client ID: {creds.get('client_id', 'Not found')}")
            token = creds.get('access_token', '')
            if token:
                masked = token[:10] + "..." + token[-10:]
                print(f"  Access Token: {masked}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing main system: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Environment Variables Migration Test")
    print("=" * 60)
    
    success = True
    
    # Test 1: Environment variable loading
    if not test_env_loading():
        success = False
    
    # Test 2: Config loading
    if not test_config_loading():
        success = False
    
    # Test 3: Main system
    if not test_main_system():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Environment variables are working correctly")
        print("✅ Credentials are properly secured")
        print("✅ Config file is clean of sensitive data")
        print("✅ Main system can load credentials from .env")
        print("\n🚀 Migration to environment variables is SUCCESSFUL!")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the issues above before proceeding.")
    
    print("=" * 60)
