#!/usr/bin/env python3
"""
Test System Startup
===================

Test the complete system startup process with environment variables.
"""

import os
import sys
import json
from dotenv import load_dotenv

def test_system_startup():
    """Test complete system startup"""
    print("🚀 Testing Complete System Startup")
    print("=" * 50)
    
    try:
        # Load environment variables
        load_dotenv()
        print("✅ Environment variables loaded")
        
        # Add src directory to path
        sys.path.append('src')
        
        # Import required modules
        from main import EMATradeSystem
        print("✅ Main system imported")
        
        # Initialize the system
        print("\n🔧 Initializing EMA Trading System...")
        system = EMATradeSystem('config/config.json')
        print("✅ System initialized successfully")
        
        # Test credential validation
        print("\n🔑 Testing credential validation...")
        validation_result = system.validate_credentials()
        print(f"✅ Credential validation: {validation_result}")
        
        # Test component initialization
        print("\n⚙️ Testing component initialization...")
        init_result = system.initialize_components()
        print(f"✅ Component initialization: {init_result}")
        
        # Check market feed type
        if hasattr(system, 'market_feed'):
            feed_type = type(system.market_feed).__name__
            print(f"✅ Market feed type: {feed_type}")
            
            if 'Mock' in feed_type:
                print("ℹ️  Using Mock Market Feed (expected if credentials are test values)")
            else:
                print("ℹ️  Using Live DhanHQ Market Feed")
        
        # Test configuration
        print(f"\n📋 Configuration Summary:")
        print(f"  Instrument: {system.config['instrument']['name']}")
        print(f"  Security ID: {system.config['instrument']['security_id']}")
        print(f"  EMA Combinations: {len(system.config['ema_combinations'])}")
        print(f"  Timeframes: {system.config['timeframes']}")
        
        # Test credentials (without exposing them)
        creds = system.config.get('dhan_credentials', {})
        print(f"  Client ID loaded: {bool(creds.get('client_id'))}")
        print(f"  Access Token loaded: {bool(creds.get('access_token'))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during system startup: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_historical_database():
    """Test historical database with environment variables"""
    print("\n📊 Testing Historical Database")
    print("-" * 30)
    
    try:
        sys.path.append('src')
        from data.historical_database import HistoricalDatabase
        
        # Load config
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        
        # Create database instance (should load credentials from env)
        db = HistoricalDatabase(
            market_hours_config=config.get('market_hours', {}),
            data_directory=config.get('data_directory', 'data')
        )
        
        print("✅ Historical database created successfully")
        print(f"  Client ID loaded: {bool(db.client_id)}")
        print(f"  Access Token loaded: {bool(db.access_token)}")
        
        # Test database info
        db_info = db.get_database_info()
        print(f"  Database status: {db_info['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing historical database: {e}")
        return False

if __name__ == "__main__":
    print("🧪 COMPLETE SYSTEM STARTUP TEST")
    print("=" * 60)
    
    success = True
    
    # Test 1: System startup
    if not test_system_startup():
        success = False
    
    # Test 2: Historical database
    if not test_historical_database():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SYSTEM STARTUP TEST PASSED!")
        print("✅ Environment variables migration is working perfectly")
        print("✅ All components initialize correctly")
        print("✅ Credentials are loaded from .env file")
        print("✅ System is ready for production use")
        print("\n🚀 The migration to environment variables is COMPLETE and WORKING!")
    else:
        print("❌ SYSTEM STARTUP TEST FAILED!")
        print("Please check the errors above.")
    
    print("=" * 60)
