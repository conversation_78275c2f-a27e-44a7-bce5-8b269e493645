#!/usr/bin/env python3
"""Quick test of environment variables"""

import os

# Manual .env loading
def load_env_manual():
    env_vars = {}
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
                    os.environ[key] = value
        return env_vars
    except Exception as e:
        print(f"Error: {e}")
        return {}

print("🧪 Quick Environment Test")
print("=" * 30)

# Load environment variables manually
env_vars = load_env_manual()
print(f"Loaded {len(env_vars)} variables")

# Test access
client_id = os.getenv('DHAN_CLIENT_ID')
access_token = os.getenv('DHAN_ACCESS_TOKEN')

print(f"Client ID: {client_id}")
print(f"Token loaded: {bool(access_token)}")

if client_id and access_token:
    print("✅ Environment variables working!")
else:
    print("❌ Environment variables not working!")

# Test config loading
try:
    import json
    with open('config/config.json', 'r') as f:
        config = json.load(f)
    
    if 'dhan_credentials' in config:
        print("❌ Credentials still in config!")
    else:
        print("✅ Config clean of credentials!")
        
except Exception as e:
    print(f"Config error: {e}")

print("=" * 30)
