#!/usr/bin/env python3
"""
Simple test to verify the EMA system components work
"""

print("Starting simple test...")

# Test 1: Basic imports
try:
    import json
    import os
    from datetime import datetime
    print("✅ Basic imports work")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")
    exit(1)

# Test 2: Check if config exists
try:
    if os.path.exists("config/config.json"):
        with open("config/config.json", "r") as f:
            config = json.load(f)
        print("✅ Config file loaded")
    else:
        print("❌ Config file not found")
except Exception as e:
    print(f"❌ Config loading failed: {e}")

# Test 3: Test EMA calculation logic (standalone)
try:
    class SimpleEMA:
        def __init__(self, period):
            self.period = period
            self.multiplier = 2.0 / (period + 1)
            self.ema = None
        
        def update(self, price):
            if self.ema is None:
                self.ema = price
            else:
                self.ema = (price * self.multiplier) + (self.ema * (1 - self.multiplier))
            return self.ema
    
    # Test with sample data
    ema5 = SimpleEMA(5)
    ema10 = SimpleEMA(10)
    
    test_prices = [100, 101, 102, 101, 103, 104, 103, 105]
    
    for price in test_prices:
        ema5_val = ema5.update(price)
        ema10_val = ema10.update(price)
        print(f"Price: {price}, EMA5: {ema5_val:.2f}, EMA10: {ema10_val:.2f}")
    
    print("✅ EMA calculation logic works")
    
except Exception as e:
    print(f"❌ EMA test failed: {e}")

# Test 4: CSV writing
try:
    import csv
    
    test_file = "test_output.csv"
    with open(test_file, 'w', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(['Datetime', 'Action', 'Price', 'EMA_Combo'])
        writer.writerow([datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'BUY', '19500.50', '5/10'])
    
    print("✅ CSV writing works")
    
    # Clean up
    if os.path.exists(test_file):
        os.remove(test_file)
        
except Exception as e:
    print(f"❌ CSV test failed: {e}")

print("\n🎉 Simple test completed!")
print("\nNext steps:")
print("1. Install dependencies: pip install dhanhq websocket-client pandas numpy")
print("2. Update config/config.json with your DhanHQ credentials")
print("3. Run: python src/main.py")
