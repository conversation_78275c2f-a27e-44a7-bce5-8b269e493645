#!/usr/bin/env python3
"""
DhanHQ Focused Examples - Specific use cases for common trading scenarios
This script provides focused examples for:
1. Real-time market data monitoring
2. Portfolio analysis
3. Historical data analysis
4. Order management

Requirements: pip install dhanhq pandas
"""

from dhanhq import dhanhq
import pandas as pd
import json
from datetime import datetime, timedelta
import time

# Configuration
CLIENT_ID = "1000000001"
ACCESS_TOKEN = "your_access_token_here"

class DhanHQTrader:
    def __init__(self, client_id, access_token):
        self.dhan = dhanhq(client_id, access_token)
        self.client_id = client_id
    
    def monitor_stocks_realtime(self, stock_list, duration_minutes=5):
        """
        Monitor real-time prices of stocks for a specified duration
        
        Args:
            stock_list (list): List of security IDs to monitor
            duration_minutes (int): How long to monitor (in minutes)
        """
        print(f"🔴 LIVE: Monitoring {len(stock_list)} stocks for {duration_minutes} minutes...")
        print("=" * 60)
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        
        while datetime.now() < end_time:
            try:
                # Get LTP data
                ltp_data = self.dhan.get_ltp_data(
                    exchange_segment=self.dhan.NSE_EQ,
                    security_id_list=stock_list
                )
                
                if ltp_data and 'data' in ltp_data:
                    current_time = datetime.now().strftime("%H:%M:%S")
                    print(f"\n⏰ {current_time}")
                    
                    for segment, stocks in ltp_data['data'].items():
                        for security_id, data in stocks.items():
                            price = data.get('last_price', 'N/A')
                            print(f"  📈 Stock {security_id}: ₹{price}")
                
                # Wait for 30 seconds before next update
                time.sleep(30)
                
            except Exception as e:
                print(f"❌ Error: {e}")
                break
        
        print("\n✅ Monitoring completed!")
    
    def analyze_portfolio_performance(self):
        """Analyze current portfolio performance"""
        print("📊 Portfolio Performance Analysis")
        print("=" * 40)
        
        try:
            # Get holdings
            holdings = self.dhan.get_holdings()
            if not holdings:
                print("No holdings found.")
                return
            
            # Get positions
            positions = self.dhan.get_positions()
            
            # Analyze holdings
            print("\n🏦 HOLDINGS ANALYSIS:")
            total_investment = 0
            total_current_value = 0
            
            for holding in holdings:
                symbol = holding.get('tradingSymbol', 'Unknown')
                qty = holding.get('totalQty', 0)
                avg_price = holding.get('avgCostPrice', 0)
                investment = qty * avg_price
                total_investment += investment
                
                print(f"  📋 {symbol}: {qty} shares @ ₹{avg_price:.2f} = ₹{investment:.2f}")
            
            print(f"\n💰 Total Investment in Holdings: ₹{total_investment:.2f}")
            
            # Analyze positions
            if positions:
                print("\n📈 POSITIONS ANALYSIS:")
                total_pnl = 0
                
                for position in positions:
                    symbol = position.get('tradingSymbol', 'Unknown')
                    net_qty = position.get('netQty', 0)
                    unrealized_pnl = position.get('unrealizedProfit', 0)
                    realized_pnl = position.get('realizedProfit', 0)
                    total_pnl += (unrealized_pnl + realized_pnl)
                    
                    if net_qty != 0:  # Only show open positions
                        print(f"  📊 {symbol}: Qty={net_qty}, Unrealized P&L=₹{unrealized_pnl:.2f}")
                
                print(f"\n💹 Total P&L from Positions: ₹{total_pnl:.2f}")
            
            # Get fund limits
            funds = self.dhan.get_fund_limits()
            if funds:
                available_balance = funds.get('availabelBalance', 0)
                print(f"💳 Available Balance: ₹{available_balance:.2f}")
        
        except Exception as e:
            print(f"❌ Error analyzing portfolio: {e}")
    
    def get_stock_analysis(self, security_id, symbol_name="Stock", days=30):
        """
        Perform technical analysis on a stock
        
        Args:
            security_id (str): Security ID of the stock
            symbol_name (str): Name of the stock for display
            days (int): Number of days of historical data
        """
        print(f"📈 Technical Analysis for {symbol_name} (ID: {security_id})")
        print("=" * 50)
        
        try:
            # Get historical data
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
            
            historical_data = self.dhan.get_historical_data(
                symbol=symbol_name,
                exchange_segment=self.dhan.NSE_EQ,
                instrument_type=self.dhan.EQUITY,
                expiry_code=0,
                from_date=start_date,
                to_date=end_date
            )
            
            if not historical_data:
                print("❌ No historical data available")
                return
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame({
                'Date': pd.to_datetime(historical_data['timestamp'], unit='s'),
                'Open': historical_data['open'],
                'High': historical_data['high'],
                'Low': historical_data['low'],
                'Close': historical_data['close'],
                'Volume': historical_data['volume']
            })
            
            # Calculate basic statistics
            current_price = df['Close'].iloc[-1]
            highest_price = df['High'].max()
            lowest_price = df['Low'].min()
            avg_volume = df['Volume'].mean()
            
            # Calculate simple moving averages
            df['SMA_5'] = df['Close'].rolling(window=5).mean()
            df['SMA_20'] = df['Close'].rolling(window=20).mean()
            
            # Calculate price change
            price_change = df['Close'].iloc[-1] - df['Close'].iloc[-2]
            price_change_pct = (price_change / df['Close'].iloc[-2]) * 100
            
            print(f"📊 PRICE ANALYSIS:")
            print(f"  Current Price: ₹{current_price:.2f}")
            print(f"  Day Change: ₹{price_change:.2f} ({price_change_pct:.2f}%)")
            print(f"  {days}-Day High: ₹{highest_price:.2f}")
            print(f"  {days}-Day Low: ₹{lowest_price:.2f}")
            print(f"  Average Volume: {avg_volume:,.0f}")
            
            # Moving averages
            sma_5 = df['SMA_5'].iloc[-1]
            sma_20 = df['SMA_20'].iloc[-1]
            
            print(f"\n📈 MOVING AVERAGES:")
            print(f"  5-Day SMA: ₹{sma_5:.2f}")
            print(f"  20-Day SMA: ₹{sma_20:.2f}")
            
            # Simple trend analysis
            if current_price > sma_5 > sma_20:
                trend = "🟢 BULLISH"
            elif current_price < sma_5 < sma_20:
                trend = "🔴 BEARISH"
            else:
                trend = "🟡 SIDEWAYS"
            
            print(f"  Trend: {trend}")
            
            # Show recent data
            print(f"\n📅 RECENT DATA (Last 5 days):")
            recent_data = df[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].tail()
            print(recent_data.to_string(index=False))
            
        except Exception as e:
            print(f"❌ Error in stock analysis: {e}")
    
    def get_market_overview(self):
        """Get overview of major market indices and stocks"""
        print("🌐 Market Overview")
        print("=" * 30)
        
        # Major stocks to monitor (example security IDs)
        major_stocks = {
            "1333": "TCS",
            "11536": "RELIANCE", 
            "1594": "INFOSYS",
            "1330": "HDFC",
            "4963": "ICICI BANK"
        }
        
        try:
            security_ids = list(major_stocks.keys())
            
            # Get LTP data
            ltp_data = self.dhan.get_ltp_data(
                exchange_segment=self.dhan.NSE_EQ,
                security_id_list=security_ids
            )
            
            # Get OHLC data
            ohlc_data = self.dhan.get_ohlc_data(
                exchange_segment=self.dhan.NSE_EQ,
                security_id_list=security_ids
            )
            
            if ltp_data and ohlc_data:
                print("📊 Major Stocks Performance:")
                print("-" * 40)
                
                for security_id in security_ids:
                    stock_name = major_stocks[security_id]
                    
                    # Get current price
                    ltp = ltp_data['data']['NSE_EQ'][security_id]['last_price']
                    
                    # Get OHLC
                    ohlc = ohlc_data['data']['NSE_EQ'][security_id]['ohlc']
                    open_price = ohlc['open']
                    high_price = ohlc['high']
                    low_price = ohlc['low']
                    prev_close = ohlc['close']
                    
                    # Calculate change
                    if prev_close > 0:
                        change = ltp - prev_close
                        change_pct = (change / prev_close) * 100
                        
                        # Determine trend emoji
                        trend_emoji = "🟢" if change > 0 else "🔴" if change < 0 else "⚪"
                        
                        print(f"{trend_emoji} {stock_name:12} ₹{ltp:8.2f} ({change:+6.2f}, {change_pct:+5.2f}%)")
                        print(f"   {'':12} O:{open_price:7.2f} H:{high_price:7.2f} L:{low_price:7.2f}")
                    else:
                        print(f"⚪ {stock_name:12} ₹{ltp:8.2f} (No prev close data)")
        
        except Exception as e:
            print(f"❌ Error getting market overview: {e}")

def main():
    """Main function with menu-driven interface"""
    
    if CLIENT_ID == "1000000001" or ACCESS_TOKEN == "your_access_token_here":
        print("⚠️  Please update CLIENT_ID and ACCESS_TOKEN with your actual credentials!")
        return
    
    trader = DhanHQTrader(CLIENT_ID, ACCESS_TOKEN)
    
    while True:
        print("\n" + "="*60)
        print("🚀 DhanHQ Trading Assistant")
        print("="*60)
        print("1. 📊 Market Overview")
        print("2. 🔴 Monitor Stocks (Real-time)")
        print("3. 📈 Stock Technical Analysis")
        print("4. 💼 Portfolio Analysis")
        print("5. 👤 User Profile")
        print("6. 🚪 Exit")
        print("="*60)
        
        choice = input("Enter your choice (1-6): ").strip()
        
        if choice == "1":
            trader.get_market_overview()
        
        elif choice == "2":
            stock_ids = input("Enter security IDs (comma-separated, e.g., 1333,11536): ").strip()
            if stock_ids:
                ids = [id.strip() for id in stock_ids.split(",")]
                duration = int(input("Monitor duration in minutes (default 2): ") or "2")
                trader.monitor_stocks_realtime(ids, duration)
        
        elif choice == "3":
            security_id = input("Enter security ID (e.g., 1333 for TCS): ").strip()
            symbol_name = input("Enter symbol name (e.g., TCS): ").strip() or "Stock"
            days = int(input("Number of days for analysis (default 30): ") or "30")
            if security_id:
                trader.get_stock_analysis(security_id, symbol_name, days)
        
        elif choice == "4":
            trader.analyze_portfolio_performance()
        
        elif choice == "5":
            try:
                profile = trader.dhan.get_profile()
                print("\n👤 User Profile:")
                print(json.dumps(profile, indent=2))
            except Exception as e:
                print(f"❌ Error: {e}")
        
        elif choice == "6":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please try again.")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
