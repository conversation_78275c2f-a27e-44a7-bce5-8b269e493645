#!/usr/bin/env python3
"""
DhanHQ Detailed Stock Analysis with Advanced Pandas Features
"""

from dhanhq import dhanhq
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Your credentials
CLIENT_ID = "1105577608"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

def fetch_and_analyze_stock(symbol, security_id, days=10):
    """Fetch and analyze individual stock data"""
    dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)
    
    try:
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days+5)).strftime("%Y-%m-%d")
        
        print(f"📊 Analyzing {symbol}...")
        
        data = dhan.historical_daily_data(
            security_id=security_id,
            exchange_segment=dhan.NSE,
            instrument_type="EQUITY",
            from_date=start_date,
            to_date=end_date
        )
        
        if data['status'] != 'success':
            print(f"❌ Error: {data.get('remarks', 'Unknown error')}")
            return None
        
        # Create DataFrame
        df = pd.DataFrame({
            'Date': pd.to_datetime(data['data']['timestamp'], unit='s'),
            'Open': data['data']['open'],
            'High': data['data']['high'],
            'Low': data['data']['low'],
            'Close': data['data']['close'],
            'Volume': data['data']['volume']
        })
        
        # Sort by date
        df = df.sort_values('Date').reset_index(drop=True)
        
        # Calculate technical indicators
        df['Daily_Return'] = df['Close'].pct_change() * 100
        df['Cumulative_Return'] = (1 + df['Daily_Return']/100).cumprod() - 1
        df['SMA_3'] = df['Close'].rolling(window=3).mean()
        df['SMA_5'] = df['Close'].rolling(window=5).mean()
        df['Volatility'] = df['Daily_Return'].rolling(window=5).std()
        df['Price_Range'] = df['High'] - df['Low']
        df['Range_Pct'] = (df['Price_Range'] / df['Open']) * 100
        
        # Volume analysis
        df['Volume_MA'] = df['Volume'].rolling(window=5).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_MA']
        
        # Support and Resistance levels
        df['Support'] = df['Low'].rolling(window=5).min()
        df['Resistance'] = df['High'].rolling(window=5).max()
        
        return df
        
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None

def display_stock_summary(df, symbol):
    """Display comprehensive stock summary"""
    if df is None or df.empty:
        return
    
    latest = df.iloc[-1]
    previous = df.iloc[-2] if len(df) > 1 else latest
    
    print(f"\n🏢 {symbol} - COMPREHENSIVE ANALYSIS")
    print("=" * 60)
    
    # Price Information
    print(f"📈 PRICE INFORMATION:")
    print(f"   Current Price:     ₹{latest['Close']:8.2f}")
    print(f"   Previous Close:    ₹{previous['Close']:8.2f}")
    print(f"   Day Change:        ₹{latest['Close'] - latest['Open']:+7.2f} ({latest['Daily_Return']:+5.2f}%)")
    print(f"   Day Range:         ₹{latest['Low']:7.2f} - ₹{latest['High']:7.2f}")
    print(f"   Range %:           {latest['Range_Pct']:5.2f}%")
    
    # Moving Averages
    print(f"\n📊 MOVING AVERAGES:")
    print(f"   3-Day SMA:         ₹{latest['SMA_3']:8.2f}")
    print(f"   5-Day SMA:         ₹{latest['SMA_5']:8.2f}")
    
    # Trend Analysis
    trend = "🟢 BULLISH" if latest['Close'] > latest['SMA_5'] else "🔴 BEARISH"
    print(f"   Trend vs 5-SMA:    {trend}")
    
    # Volume Analysis
    print(f"\n📊 VOLUME ANALYSIS:")
    print(f"   Today's Volume:    {latest['Volume']:12,.0f}")
    print(f"   5-Day Avg Volume:  {latest['Volume_MA']:12,.0f}")
    print(f"   Volume Ratio:      {latest['Volume_Ratio']:8.2f}x")
    
    volume_signal = "🟢 HIGH" if latest['Volume_Ratio'] > 1.5 else "🟡 NORMAL" if latest['Volume_Ratio'] > 0.8 else "🔴 LOW"
    print(f"   Volume Signal:     {volume_signal}")
    
    # Support & Resistance
    print(f"\n🎯 SUPPORT & RESISTANCE:")
    print(f"   Support Level:     ₹{latest['Support']:8.2f}")
    print(f"   Resistance Level:  ₹{latest['Resistance']:8.2f}")
    print(f"   Distance to Support: {((latest['Close'] - latest['Support']) / latest['Close'] * 100):5.2f}%")
    print(f"   Distance to Resistance: {((latest['Resistance'] - latest['Close']) / latest['Close'] * 100):5.2f}%")
    
    # Performance Metrics
    total_return = df['Cumulative_Return'].iloc[-1] * 100
    max_return = df['Cumulative_Return'].max() * 100
    min_return = df['Cumulative_Return'].min() * 100
    avg_volatility = df['Volatility'].mean()
    
    print(f"\n📈 PERFORMANCE METRICS:")
    print(f"   Total Return:      {total_return:+6.2f}%")
    print(f"   Max Return:        {max_return:+6.2f}%")
    print(f"   Min Return:        {min_return:+6.2f}%")
    print(f"   Avg Volatility:    {avg_volatility:6.2f}%")
    
    # Risk Assessment
    if avg_volatility < 1.5:
        risk_level = "🟢 LOW RISK"
    elif avg_volatility < 3.0:
        risk_level = "🟡 MEDIUM RISK"
    else:
        risk_level = "🔴 HIGH RISK"
    
    print(f"   Risk Level:        {risk_level}")

def display_daily_breakdown(df, symbol):
    """Display day-by-day breakdown"""
    print(f"\n📅 {symbol} - DAILY BREAKDOWN (Last 7 Days)")
    print("=" * 80)
    
    # Get last 7 days
    recent_df = df.tail(7).copy()
    
    # Format for display
    display_df = recent_df[['Date', 'Open', 'High', 'Low', 'Close', 'Daily_Return', 'Volume']].copy()
    display_df['Date'] = display_df['Date'].dt.strftime('%Y-%m-%d %a')
    display_df['Volume'] = display_df['Volume'].apply(lambda x: f"{x:,.0f}")
    display_df = display_df.round(2)
    
    print(display_df.to_string(index=False))

def display_technical_signals(df, symbol):
    """Display technical trading signals"""
    print(f"\n🎯 {symbol} - TECHNICAL SIGNALS")
    print("=" * 40)
    
    latest = df.iloc[-1]
    previous = df.iloc[-2] if len(df) > 1 else latest
    
    signals = []
    
    # Price vs Moving Average
    if latest['Close'] > latest['SMA_5']:
        signals.append("🟢 Price above 5-day SMA (Bullish)")
    else:
        signals.append("🔴 Price below 5-day SMA (Bearish)")
    
    # Moving Average Crossover
    if len(df) > 1:
        if latest['SMA_3'] > latest['SMA_5'] and previous['SMA_3'] <= previous['SMA_5']:
            signals.append("🟢 Golden Cross: 3-SMA crossed above 5-SMA")
        elif latest['SMA_3'] < latest['SMA_5'] and previous['SMA_3'] >= previous['SMA_5']:
            signals.append("🔴 Death Cross: 3-SMA crossed below 5-SMA")
    
    # Volume Signal
    if latest['Volume_Ratio'] > 1.5:
        signals.append("🟢 High volume breakout")
    elif latest['Volume_Ratio'] < 0.5:
        signals.append("🔴 Low volume (weak momentum)")
    
    # Support/Resistance
    support_distance = (latest['Close'] - latest['Support']) / latest['Close'] * 100
    resistance_distance = (latest['Resistance'] - latest['Close']) / latest['Close'] * 100
    
    if support_distance < 2:
        signals.append("🟡 Near support level (potential bounce)")
    if resistance_distance < 2:
        signals.append("🟡 Near resistance level (potential reversal)")
    
    # Volatility
    if latest['Volatility'] > df['Volatility'].mean() * 1.5:
        signals.append("🔴 High volatility (increased risk)")
    
    for signal in signals:
        print(f"   {signal}")
    
    if not signals:
        print("   ⚪ No significant signals detected")

def compare_stocks(stocks_data):
    """Compare multiple stocks"""
    print(f"\n🔍 STOCK COMPARISON")
    print("=" * 50)
    
    comparison_data = []
    
    for symbol, df in stocks_data.items():
        if df is not None and not df.empty:
            latest = df.iloc[-1]
            comparison_data.append({
                'Symbol': symbol,
                'Price': latest['Close'],
                'Daily_Return%': latest['Daily_Return'],
                'Volatility%': df['Volatility'].mean(),
                'Volume_Ratio': latest['Volume_Ratio'],
                'Trend': '🟢' if latest['Close'] > latest['SMA_5'] else '🔴'
            })
    
    if comparison_data:
        comp_df = pd.DataFrame(comparison_data)
        comp_df = comp_df.sort_values('Daily_Return%', ascending=False)
        print(comp_df.round(2).to_string(index=False))

def main():
    """Main analysis function"""
    print("🚀 DhanHQ Detailed Stock Analysis")
    print("=" * 40)
    
    # Stocks to analyze
    stocks = {
        "TCS": "1333",
        "RELIANCE": "11536",
        "INFOSYS": "1594"
    }
    
    stocks_data = {}
    
    # Analyze each stock
    for symbol, security_id in stocks.items():
        df = fetch_and_analyze_stock(symbol, security_id, days=10)
        stocks_data[symbol] = df
        
        if df is not None:
            display_stock_summary(df, symbol)
            display_daily_breakdown(df, symbol)
            display_technical_signals(df, symbol)
        
        time.sleep(1)  # Rate limiting
    
    # Compare stocks
    compare_stocks(stocks_data)
    
    print(f"\n✅ Analysis completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
