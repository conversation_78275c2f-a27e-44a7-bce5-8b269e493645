#!/usr/bin/env python3
"""
DhanHQ Data Analysis with Pandas
Fetch 5 days of data for multiple instruments and display using pandas
"""

from dhanhq import dhanhq
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Your credentials
CLIENT_ID = "**********"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************************************************************************.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Instrument mapping
INSTRUMENTS = {
    "TCS": "1333",
    "RELIANCE": "11536", 
    "INFOSYS": "1594",
    "HDFC_BANK": "1330",
    "ICICI_BANK": "4963",
    "WIPRO": "3787",
    "ITC": "1660",
    "BHARTI_AIRTEL": "275"
}

def fetch_historical_data(dhan, symbol, security_id, days=5):
    """Fetch historical data for a given instrument"""
    try:
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days+2)).strftime("%Y-%m-%d")  # Extra days for weekends
        
        print(f"📊 Fetching {days} days data for {symbol}...")
        
        data = dhan.historical_daily_data(
            security_id=security_id,
            exchange_segment=dhan.NSE,
            instrument_type="EQUITY",
            from_date=start_date,
            to_date=end_date
        )
        
        if data['status'] == 'success':
            return data['data']
        else:
            print(f"❌ Error fetching data for {symbol}: {data.get('remarks', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ Exception fetching data for {symbol}: {e}")
        return None

def create_dataframe(symbol, data):
    """Convert API data to pandas DataFrame"""
    if not data:
        return None
    
    try:
        df = pd.DataFrame({
            'Date': pd.to_datetime(data['timestamp'], unit='s'),
            'Open': data['open'],
            'High': data['high'],
            'Low': data['low'],
            'Close': data['close'],
            'Volume': data['volume']
        })
        
        # Add symbol column
        df['Symbol'] = symbol
        
        # Calculate additional metrics
        df['Daily_Change'] = df['Close'] - df['Open']
        df['Daily_Change_Pct'] = ((df['Close'] - df['Open']) / df['Open'] * 100).round(2)
        df['High_Low_Range'] = df['High'] - df['Low']
        df['Range_Pct'] = ((df['High_Low_Range']) / df['Open'] * 100).round(2)
        
        # Sort by date (most recent first)
        df = df.sort_values('Date', ascending=False)
        
        return df
        
    except Exception as e:
        print(f"❌ Error creating DataFrame for {symbol}: {e}")
        return None

def display_summary_stats(df):
    """Display summary statistics for the data"""
    print("\n📈 SUMMARY STATISTICS")
    print("=" * 60)
    
    # Group by symbol for summary
    summary = df.groupby('Symbol').agg({
        'Close': ['last', 'mean', 'min', 'max'],
        'Volume': ['mean', 'sum'],
        'Daily_Change_Pct': ['mean', 'std'],
        'Range_Pct': 'mean'
    }).round(2)
    
    # Flatten column names
    summary.columns = ['Current_Price', 'Avg_Price', 'Min_Price', 'Max_Price', 
                      'Avg_Volume', 'Total_Volume', 'Avg_Change%', 'Volatility%', 'Avg_Range%']
    
    print(summary)

def display_latest_prices(df):
    """Display latest prices for all instruments"""
    print("\n💰 LATEST PRICES (Most Recent Trading Day)")
    print("=" * 70)
    
    latest = df.groupby('Symbol').first()[['Date', 'Open', 'High', 'Low', 'Close', 'Daily_Change_Pct', 'Volume']]
    latest = latest.sort_values('Daily_Change_Pct', ascending=False)
    
    print(latest.to_string())

def display_top_performers(df):
    """Display top and bottom performers"""
    print("\n🏆 TOP PERFORMERS (Average Daily Change %)")
    print("=" * 50)
    
    performance = df.groupby('Symbol')['Daily_Change_Pct'].mean().sort_values(ascending=False)
    
    print("🟢 TOP GAINERS:")
    for symbol, change in performance.head(3).items():
        print(f"   {symbol:12} {change:+6.2f}%")
    
    print("\n🔴 TOP LOSERS:")
    for symbol, change in performance.tail(3).items():
        print(f"   {symbol:12} {change:+6.2f}%")

def display_volume_analysis(df):
    """Display volume analysis"""
    print("\n📊 VOLUME ANALYSIS")
    print("=" * 40)
    
    volume_stats = df.groupby('Symbol')['Volume'].agg(['mean', 'max', 'min']).round(0)
    volume_stats.columns = ['Avg_Volume', 'Max_Volume', 'Min_Volume']
    volume_stats = volume_stats.sort_values('Avg_Volume', ascending=False)
    
    print(volume_stats.to_string())

def display_volatility_analysis(df):
    """Display volatility analysis"""
    print("\n📈 VOLATILITY ANALYSIS (Daily Range %)")
    print("=" * 45)
    
    volatility = df.groupby('Symbol')['Range_Pct'].agg(['mean', 'max', 'min']).round(2)
    volatility.columns = ['Avg_Range%', 'Max_Range%', 'Min_Range%']
    volatility = volatility.sort_values('Avg_Range%', ascending=False)
    
    print(volatility.to_string())

def display_daily_trends(df):
    """Display day-wise trends"""
    print("\n📅 DAILY TRENDS (Last 5 Days)")
    print("=" * 50)
    
    # Get last 5 days data
    latest_dates = df['Date'].unique()[:5]
    
    for date in latest_dates:
        day_data = df[df['Date'] == date]
        if not day_data.empty:
            avg_change = day_data['Daily_Change_Pct'].mean()
            gainers = len(day_data[day_data['Daily_Change_Pct'] > 0])
            losers = len(day_data[day_data['Daily_Change_Pct'] < 0])
            
            trend_emoji = "🟢" if avg_change > 0 else "🔴" if avg_change < 0 else "⚪"
            
            print(f"{trend_emoji} {date.strftime('%Y-%m-%d %a'):15} Avg: {avg_change:+5.2f}% | "
                  f"Gainers: {gainers} | Losers: {losers}")

def create_correlation_matrix(df):
    """Create correlation matrix of stock prices"""
    print("\n🔗 PRICE CORRELATION MATRIX")
    print("=" * 35)
    
    # Pivot data to have symbols as columns
    pivot_df = df.pivot(index='Date', columns='Symbol', values='Close')
    
    # Calculate correlation
    correlation = pivot_df.corr().round(2)
    
    print(correlation.to_string())

def main():
    """Main function to fetch and analyze data"""
    print("🚀 DhanHQ Data Analysis with Pandas")
    print("=" * 40)
    
    # Initialize DhanHQ client
    dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)
    
    all_dataframes = []
    
    # Fetch data for all instruments
    for symbol, security_id in INSTRUMENTS.items():
        data = fetch_historical_data(dhan, symbol, security_id, days=5)
        
        if data:
            df = create_dataframe(symbol, data)
            if df is not None:
                all_dataframes.append(df)
        
        # Rate limiting
        time.sleep(1)
    
    if not all_dataframes:
        print("❌ No data fetched successfully!")
        return
    
    # Combine all dataframes
    combined_df = pd.concat(all_dataframes, ignore_index=True)
    
    print(f"\n✅ Successfully fetched data for {len(all_dataframes)} instruments")
    print(f"📊 Total records: {len(combined_df)}")
    
    # Display various analyses
    display_latest_prices(combined_df)
    display_summary_stats(combined_df)
    display_top_performers(combined_df)
    display_volume_analysis(combined_df)
    display_volatility_analysis(combined_df)
    display_daily_trends(combined_df)
    create_correlation_matrix(combined_df)
    
    # Save to CSV for further analysis
    csv_filename = f"stock_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    combined_df.to_csv(csv_filename, index=False)
    print(f"\n💾 Data saved to: {csv_filename}")
    
    # Display sample of raw data
    print(f"\n📋 SAMPLE DATA (First 10 records)")
    print("=" * 80)
    sample_df = combined_df[['Symbol', 'Date', 'Open', 'High', 'Low', 'Close', 'Daily_Change_Pct', 'Volume']].head(10)
    print(sample_df.to_string(index=False))

if __name__ == "__main__":
    main()
