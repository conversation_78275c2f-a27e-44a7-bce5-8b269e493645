#!/usr/bin/env python3
"""
DhanHQ API Examples - Comprehensive sample code for DhanHQ v2 APIs
This script demonstrates how to use various DhanHQ APIs to fetch market data,
portfolio information, and execute trades.

Requirements:
- pip install dhanhq requests pandas

Setup:
1. Get your access token from https://web.dhan.co (My Profile -> Access DhanHQ APIs)
2. Replace CLIENT_ID and ACCESS_TOKEN with your actual credentials
"""

import requests
import json
from datetime import datetime, timedelta
import pandas as pd
from dhanhq import dhanhq

# Configuration - Replace with your actual credentials
CLIENT_ID = "1000000001"  # Replace with your actual client ID
ACCESS_TOKEN = "your_access_token_here"  # Replace with your actual access token

# API Base URL
BASE_URL = "https://api.dhan.co/v2"

# Headers for API requests
HEADERS = {
    'Content-Type': 'application/json',
    'access-token': ACCESS_TOKEN,
    'client-id': CLIENT_ID
}

class DhanHQAPI:
    """DhanHQ API wrapper class for easy interaction with DhanHQ APIs"""
    
    def __init__(self, client_id, access_token):
        self.client_id = client_id
        self.access_token = access_token
        self.headers = {
            'Content-Type': 'application/json',
            'access-token': access_token,
            'client-id': client_id
        }
        self.base_url = "https://api.dhan.co/v2"
        
        # Initialize DhanHQ Python client
        self.dhan = dhanhq(client_id, access_token)
    
    def get_user_profile(self):
        """Get user profile information"""
        try:
            url = f"{self.base_url}/profile"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching user profile: {e}")
            return None
    
    def get_ltp_data(self, instruments):
        """
        Get Last Traded Price (LTP) for multiple instruments
        
        Args:
            instruments (dict): Dictionary with exchange segments as keys and security IDs as values
            Example: {"NSE_EQ": [1333, 11536], "NSE_FNO": [49081, 49082]}
        """
        try:
            url = f"{self.base_url}/marketfeed/ltp"
            response = requests.post(url, headers=self.headers, json=instruments)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching LTP data: {e}")
            return None
    
    def get_ohlc_data(self, instruments):
        """
        Get OHLC (Open, High, Low, Close) data for multiple instruments
        
        Args:
            instruments (dict): Dictionary with exchange segments as keys and security IDs as values
        """
        try:
            url = f"{self.base_url}/marketfeed/ohlc"
            response = requests.post(url, headers=self.headers, json=instruments)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching OHLC data: {e}")
            return None
    
    def get_market_depth(self, instruments):
        """
        Get market depth data including bid/ask prices and quantities
        
        Args:
            instruments (dict): Dictionary with exchange segments as keys and security IDs as values
        """
        try:
            url = f"{self.base_url}/marketfeed/quote"
            response = requests.post(url, headers=self.headers, json=instruments)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching market depth: {e}")
            return None
    
    def get_historical_data(self, security_id, exchange_segment, instrument, from_date, to_date, expiry_code=0, oi=False):
        """
        Get historical daily data for an instrument
        
        Args:
            security_id (str): Security ID of the instrument
            exchange_segment (str): Exchange segment (e.g., "NSE_EQ", "NSE_FNO")
            instrument (str): Instrument type (e.g., "EQUITY", "FUTIDX")
            from_date (str): Start date in YYYY-MM-DD format
            to_date (str): End date in YYYY-MM-DD format
            expiry_code (int): Expiry code for derivatives (optional)
            oi (bool): Include open interest data (optional)
        """
        try:
            url = f"{self.base_url}/charts/historical"
            payload = {
                "securityId": security_id,
                "exchangeSegment": exchange_segment,
                "instrument": instrument,
                "expiryCode": expiry_code,
                "oi": oi,
                "fromDate": from_date,
                "toDate": to_date
            }
            response = requests.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching historical data: {e}")
            return None
    
    def get_intraday_data(self, security_id, exchange_segment, instrument, interval, from_date, to_date, oi=False):
        """
        Get intraday historical data for an instrument
        
        Args:
            security_id (str): Security ID of the instrument
            exchange_segment (str): Exchange segment
            instrument (str): Instrument type
            interval (str): Time interval ("1", "5", "15", "25", "60" minutes)
            from_date (str): Start datetime in YYYY-MM-DD HH:MM:SS format
            to_date (str): End datetime in YYYY-MM-DD HH:MM:SS format
            oi (bool): Include open interest data (optional)
        """
        try:
            url = f"{self.base_url}/charts/intraday"
            payload = {
                "securityId": security_id,
                "exchangeSegment": exchange_segment,
                "instrument": instrument,
                "interval": interval,
                "oi": oi,
                "fromDate": from_date,
                "toDate": to_date
            }
            response = requests.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching intraday data: {e}")
            return None
    
    def get_holdings(self):
        """Get holdings in demat account"""
        try:
            url = f"{self.base_url}/holdings"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching holdings: {e}")
            return None
    
    def get_positions(self):
        """Get open positions"""
        try:
            url = f"{self.base_url}/positions"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching positions: {e}")
            return None

def print_formatted_json(data, title="Data"):
    """Helper function to print JSON data in a formatted way"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(json.dumps(data, indent=2))
    print(f"{'='*50}\n")

def create_dataframe_from_historical(data):
    """Convert historical data to pandas DataFrame"""
    if not data or 'open' not in data:
        return None
    
    df = pd.DataFrame({
        'timestamp': pd.to_datetime(data['timestamp'], unit='s'),
        'open': data['open'],
        'high': data['high'],
        'low': data['low'],
        'close': data['close'],
        'volume': data['volume']
    })
    
    if 'open_interest' in data:
        df['open_interest'] = data['open_interest']
    
    return df

def main():
    """Main function demonstrating various DhanHQ API calls"""
    
    # Initialize DhanHQ API client
    api = DhanHQAPI(CLIENT_ID, ACCESS_TOKEN)
    
    print("DhanHQ API Examples")
    print("==================")
    
    # 1. Get User Profile
    print("\n1. Fetching User Profile...")
    profile = api.get_user_profile()
    if profile:
        print_formatted_json(profile, "User Profile")
    
    # 2. Get LTP Data for multiple instruments
    print("\n2. Fetching LTP Data...")
    instruments_ltp = {
        "NSE_EQ": [1333, 11536],  # TCS, RELIANCE (example security IDs)
        "NSE_FNO": [49081]        # Example F&O instrument
    }
    ltp_data = api.get_ltp_data(instruments_ltp)
    if ltp_data:
        print_formatted_json(ltp_data, "LTP Data")
    
    # 3. Get OHLC Data
    print("\n3. Fetching OHLC Data...")
    ohlc_data = api.get_ohlc_data(instruments_ltp)
    if ohlc_data:
        print_formatted_json(ohlc_data, "OHLC Data")
    
    # 4. Get Market Depth
    print("\n4. Fetching Market Depth...")
    market_depth = api.get_market_depth({"NSE_EQ": [1333]})
    if market_depth:
        print_formatted_json(market_depth, "Market Depth")
    
    # 5. Get Historical Data (last 30 days)
    print("\n5. Fetching Historical Data...")
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    historical_data = api.get_historical_data(
        security_id="1333",
        exchange_segment="NSE_EQ",
        instrument="EQUITY",
        from_date=start_date.strftime("%Y-%m-%d"),
        to_date=end_date.strftime("%Y-%m-%d")
    )
    
    if historical_data:
        print("Historical Data (First 5 records):")
        df = create_dataframe_from_historical(historical_data)
        if df is not None:
            print(df.head())
        else:
            print_formatted_json(historical_data, "Historical Data")
    
    # 6. Get Intraday Data (last 2 days, 5-minute intervals)
    print("\n6. Fetching Intraday Data...")
    end_datetime = datetime.now()
    start_datetime = end_datetime - timedelta(days=2)
    
    intraday_data = api.get_intraday_data(
        security_id="1333",
        exchange_segment="NSE_EQ",
        instrument="EQUITY",
        interval="5",
        from_date=start_datetime.strftime("%Y-%m-%d %H:%M:%S"),
        to_date=end_datetime.strftime("%Y-%m-%d %H:%M:%S")
    )
    
    if intraday_data:
        print("Intraday Data (First 5 records):")
        df = create_dataframe_from_historical(intraday_data)
        if df is not None:
            print(df.head())
        else:
            print_formatted_json(intraday_data, "Intraday Data")
    
    # 7. Get Holdings
    print("\n7. Fetching Holdings...")
    holdings = api.get_holdings()
    if holdings:
        print_formatted_json(holdings, "Holdings")
    
    # 8. Get Positions
    print("\n8. Fetching Positions...")
    positions = api.get_positions()
    if positions:
        print_formatted_json(positions, "Positions")

if __name__ == "__main__":
    # Check if credentials are set
    if CLIENT_ID == "1105577608" or ACCESS_TOKEN == "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw":
        print("⚠️  Please update CLIENT_ID and ACCESS_TOKEN with your actual credentials!")
        print("Get your credentials from: https://web.dhan.co (My Profile -> Access DhanHQ APIs)")
    else:
        main()
