#!/usr/bin/env python3
"""
Easy Run Script for EMA Crossover System
========================================

This script provides an easy way to run the EMA crossover system
with different modes and options.

Usage:
    python run.py [mode]
    
Modes:
    live    - Run with live DhanHQ data (default)
    test    - Run test suite
    mock    - Run with mock data for testing
    help    - Show this help

Author: AI Assistant
Date: 2025
"""

import sys
import os
import subprocess


def run_live_system():
    """Run the live trading system"""
    print("🚀 Starting Live EMA Crossover System...")
    print("Make sure you have updated your DhanHQ credentials in config/config.json")
    print("Press Ctrl+C to stop the system")
    print("-" * 60)
    
    try:
        subprocess.run([sys.executable, "src/main.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 System stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running system: {e}")


def run_test_suite():
    """Run the test suite"""
    print("🧪 Running Test Suite...")
    print("-" * 60)
    
    try:
        subprocess.run([sys.executable, "test_system.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running tests: {e}")


def run_mock_system():
    """Run system with mock data"""
    print("🎭 Starting Mock EMA Crossover System...")
    print("This will simulate market data for testing")
    print("Press Ctrl+C to stop the system")
    print("-" * 60)
    
    # Modify main.py to use mock feed temporarily
    print("Note: To use mock mode, edit src/main.py and replace")
    print("DhanMarketFeed with MockMarketFeed in the initialize_components method")
    
    try:
        subprocess.run([sys.executable, "src/main.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Mock system stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running mock system: {e}")


def show_help():
    """Show help information"""
    print(__doc__)
    print("\nProject Structure:")
    print("├── src/           # Source code")
    print("├── config/        # Configuration files")
    print("├── data/          # Output CSV files")
    print("├── logs/          # System logs")
    print("└── requirements.txt")
    print("\nQuick Start:")
    print("1. pip install -r requirements.txt")
    print("2. Update config/config.json with your DhanHQ credentials")
    print("3. python run.py live")


def check_setup():
    """Check if the system is properly set up"""
    issues = []
    
    # Check if config file exists
    if not os.path.exists("config/config.json"):
        issues.append("❌ config/config.json not found")
    
    # Check if required directories exist
    for directory in ["src", "data", "logs"]:
        if not os.path.exists(directory):
            issues.append(f"❌ {directory}/ directory not found")
    
    # Check if main files exist
    required_files = [
        "src/main.py",
        "src/ema.py", 
        "src/strategy.py",
        "src/logger.py",
        "src/market_feed.py",
        "requirements.txt"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            issues.append(f"❌ {file} not found")
    
    if issues:
        print("⚠️  Setup Issues Found:")
        for issue in issues:
            print(f"   {issue}")
        print("\nPlease fix these issues before running the system.")
        return False
    else:
        print("✅ System setup looks good!")
        return True


def main():
    """Main entry point"""
    print("=" * 60)
    print("EMA CROSSOVER SYSTEM - RUNNER")
    print("=" * 60)
    print()
    
    # Check setup first
    if not check_setup():
        return
    
    # Get mode from command line
    mode = sys.argv[1] if len(sys.argv) > 1 else "live"
    
    if mode == "live":
        run_live_system()
    elif mode == "test":
        run_test_suite()
    elif mode == "mock":
        run_mock_system()
    elif mode == "help":
        show_help()
    else:
        print(f"❌ Unknown mode: {mode}")
        print("Available modes: live, test, mock, help")
        print("Use 'python run.py help' for more information")


if __name__ == "__main__":
    main()
