# 🚀 Enhanced NIFTY 50 EMA Crossover Trading System

## 📋 Overview

This enhanced system provides **automated EMA crossover signal detection** for NIFTY 50 with the following key features:

- ✅ **Market Hours Awareness**: Understands Indian market hours (9:15 AM - 3:15 PM IST)
- ✅ **Background Mode**: Runs as a daemon, automatically starting/stopping with market hours
- ✅ **Daily CSV Files**: Single CSV file per date with all crossover records
- ✅ **EMA State Persistence**: Maintains EMA calculations across script restarts within the same day
- ✅ **Simplified Focus**: Only 5/10 EMA crossover (5 EMA cross above 10 = BUY, 5 EMA cross below 10 = SELL)

## 🎯 Key Features

### Market Hours Management
- **Trading Hours**: 9:15 AM - 3:15 PM IST (Monday-Friday)
- **Auto Start/Stop**: System automatically activates during market hours
- **Session Persistence**: EMA state maintained throughout the trading day
- **Daily Reset**: Fresh EMA calculations start each trading day

### Signal Generation
- **5/10 EMA Crossover**: Simple and effective crossover strategy
- **Real-time Processing**: Live tick data → 1-minute candles → EMA calculation → Signal detection
- **P&L Tracking**: Automatic profit/loss calculation for each signal

### Data Management
- **Daily CSV Files**: Format: `nifty50_ema_signals_YYYYMMDD.csv`
- **Append Mode**: New signals added to existing daily file
- **State Recovery**: System can restart anytime and continue from where it left off

## 📁 File Structure

```
├── src/
│   ├── main.py              # Main system entry point
│   ├── market_hours.py      # Market hours management
│   ├── market_feed.py       # Data feed (DhanHQ/Mock)
│   ├── strategy.py          # EMA strategy implementation
│   ├── ema.py              # EMA calculation engine
│   └── logger.py           # Daily CSV logging
├── config/
│   └── config.json         # System configuration
├── data/                   # Daily CSV files stored here
├── logs/                   # System logs
├── ema_daemon.py          # Daemon management script
└── README_ENHANCED.md     # This file
```

## 🚀 Usage

### 1. Foreground Mode (Interactive)
```bash
# Start in foreground mode
python src/main.py

# The system will:
# - Check market hours
# - Ask if you want to wait for market open (if closed)
# - Start processing live data
# - Show signals in console
# - Stop with Ctrl+C
```

### 2. Background Mode (Daemon-like)
```bash
# Start in background mode
python src/main.py --background

# The system will:
# - Run continuously in background
# - Wait for market hours automatically
# - Start/stop data feed with market open/close
# - Generate daily summaries
# - Run until manually stopped
```

### 3. Daemon Management (Recommended)
```bash
# Start as daemon
python ema_daemon.py start

# Check status
python ema_daemon.py status

# View logs
python ema_daemon.py logs

# Follow logs in real-time
python ema_daemon.py logs --follow

# Stop daemon
python ema_daemon.py stop

# Restart daemon
python ema_daemon.py restart
```

## 📊 CSV Output Format

Daily CSV file: `data/nifty50_ema_signals_20250529.csv`

```csv
Date,Time,Action,Price,EMA5_Value,EMA10_Value,PnL,Cumulative_PnL,Signal_Number
2025-05-29,09:30:15,BUY,19500.50,19498.25,19495.80,0.00,0.00,1
2025-05-29,10:45:22,SELL,19485.75,19490.60,19493.45,-14.75,-14.75,2
2025-05-29,11:20:08,BUY,19510.25,19505.40,19502.15,24.50,9.75,3
```

## ⚙️ Configuration

The system uses `config/config.json`:

```json
{
  "dhan_credentials": {
    "client_id": "YOUR_CLIENT_ID_HERE",
    "access_token": "YOUR_ACCESS_TOKEN_HERE"
  },
  "instrument": {
    "name": "NIFTY 50",
    "security_id": "13",
    "exchange_segment": "IDX_I"
  },
  "ema_combinations": [
    {
      "short_ema": 5,
      "long_ema": 10
    }
  ],
  "timeframes": ["1min"],
  "market_hours": {
    "timezone": "Asia/Kolkata",
    "start_time": "09:15",
    "end_time": "15:15",
    "trading_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
  }
}
```

## 🔧 Installation & Setup

1. **Install Dependencies**:
```bash
uv pip install pytz websocket-client requests
```

2. **Configure Credentials**:
   - Edit `config/config.json`
   - Add your DhanHQ client_id and access_token
   - Or leave as default to use mock data for testing

3. **Test the System**:
```bash
python test_enhanced_system.py
```

## 📈 Signal Logic

### BUY Signal
- **Condition**: 5-period EMA crosses **above** 10-period EMA
- **Action**: Generate BUY signal
- **CSV Record**: Action=BUY, Price=current_price, P&L=0.00 (entry)

### SELL Signal
- **Condition**: 5-period EMA crosses **below** 10-period EMA
- **Action**: Generate SELL signal
- **CSV Record**: Action=SELL, Price=current_price, P&L=calculated

## 🕐 Market Hours Behavior

### During Market Hours (9:15 AM - 3:15 PM IST)
- ✅ Process live tick data
- ✅ Generate 1-minute candles
- ✅ Calculate EMAs
- ✅ Detect crossover signals
- ✅ Log signals to daily CSV

### Outside Market Hours
- 🔴 Stop data processing
- 📊 Generate daily summary
- ⏰ Wait for next market session
- 💾 Preserve EMA state for next day reset

## 🔄 State Management

### Daily Reset (New Trading Day)
- EMA calculations reset to fresh state
- New daily CSV file created
- P&L tracking starts from zero
- Previous day's data preserved

### Intraday Restart
- EMA state continues from last calculation
- Signals append to existing daily CSV
- P&L tracking continues from last value
- No data loss

## 📝 Logging

### Console Output
```
🟢 MARKET OPEN - Closes in 2h 49m
🔔 BUY Signal: 1min 5_10 @ 19500.50 (EMA5: 19498.25, EMA10: 19495.80) P&L: 0.00
📝 Signal #1 logged: BUY @ 19500.50
```

### Log Files
- **System Logs**: `logs/ema_system_YYYYMMDD.log`
- **Daemon Logs**: `logs/ema_daemon.log`

## 🎯 Production Deployment

### Recommended Setup
```bash
# 1. Start as daemon
python ema_daemon.py start

# 2. Monitor status
python ema_daemon.py status

# 3. Check daily CSV files
ls -la data/nifty50_ema_signals_*.csv

# 4. Monitor logs
tail -f logs/ema_system_$(date +%Y%m%d).log
```

### Automatic Startup (Optional)
Add to crontab for automatic startup:
```bash
# Start EMA system at 9:00 AM every weekday
0 9 * * 1-5 cd /path/to/ema-system && python ema_daemon.py start
```

## 🛠️ Troubleshooting

### Common Issues

1. **No Signals Generated**
   - Check if market is open
   - Verify EMA has enough data (need 10+ candles for 10-period EMA)
   - Check logs for errors

2. **WebSocket Connection Issues**
   - Verify DhanHQ credentials
   - Check internet connection
   - System will auto-retry connections

3. **CSV File Issues**
   - Check `data/` directory permissions
   - Verify disk space
   - Check logs for file errors

### Debug Commands
```bash
# Check system status
python ema_daemon.py status

# View recent logs
python ema_daemon.py logs

# Test configuration
python test_enhanced_system.py

# Manual foreground run
python src/main.py
```

## 📞 Support

The system is designed to be robust and self-healing:
- Automatic reconnection on connection loss
- Graceful error handling
- Comprehensive logging
- State persistence across restarts

**🎉 Your enhanced NIFTY 50 EMA crossover system is ready for production use!**
